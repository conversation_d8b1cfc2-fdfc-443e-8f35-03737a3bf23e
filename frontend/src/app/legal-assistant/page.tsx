'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ProtectedLayout from '@/components/layout/ProtectedLayout';
import ChatPlayground, {
  type PlaygroundMode,
  type Message,
  type RetrieveResponse,
  type QueryResponse,
  type AgentResponse
} from '@/components/ChatPlayground';
import DocumentsBrowser, {
  type SearchResult
} from '@/components/DocumentsBrowser';
import { apiService } from '@/lib/services/apiService';
import { useInView } from 'react-intersection-observer';

interface SearchResponse {
  results: SearchResult[];
  total_count: number;
  page: number;
  page_size: number;
  total_pages: number;
  next_offset?: string;
  has_more: boolean;
  query?: string;
  processing_time_ms: number;
}

const LegalAssistantPage = () => {
  // State management
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'नमस्कार! म तपाईंको कानुनी सहायक हुँ। म तपाईंलाई कानुनी दस्तावेजहरू खोज्न र कानुनी प्रश्नहरूको जवाफ दिन मद्दत गर्न सक्छु। तपाईं के खोज्दै हुनुहुन्छ?',
      role: 'assistant',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [nextOffset, setNextOffset] = useState<string | undefined>();
  const [playgroundMode, setPlaygroundMode] = useState<PlaygroundMode>('query');
  const [sourceNodes, setSourceNodes] = useState<RetrieveResponse[]>([]);
  const [queryResponse, setQueryResponse] = useState<string>('');
  const [agentResponse, setAgentResponse] = useState<AgentResponse | null>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  // Load initial documents
  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async (page: number = 1, append: boolean = false) => {
    setIsSearching(true);
    try {
      const response: SearchResponse = await apiService.searchAllDocuments({
        page: page,
        page_size: 12
      });

      if (append) {
        setSearchResults(prev => [...prev, ...response.results]);
      } else {
        setSearchResults(response.results);
      }

      setCurrentPage(page);
      setHasMore(response.has_more);
      setNextOffset(response.next_offset);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const loadMoreDocuments = () => {
    if (hasMore && !isSearching) {
      loadDocuments(searchQuery, currentPage + 1, true);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      role: 'user',
      timestamp: new Date()
    };

    const currentInput = inputMessage;
    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    // Clear previous results
    setSourceNodes([]);
    setQueryResponse('');

    try {
      if (playgroundMode === 'retrieve') {
        // Use retrieve endpoint - only get source nodes
        const retrieveResults = await apiService.retrieveSourceNodes(currentInput, 5);
        setSourceNodes(retrieveResults);

        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `तपाईंको खोज "${currentInput}" को लागि ${retrieveResults.length} वटा सम्बन्धित दस्तावेजहरू फेला परेका छन्। दायाँ तिरका स्रोत नोडहरू हेर्नुहोस्।`,
          role: 'assistant',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, assistantMessage]);
      } else if (playgroundMode === 'agent') {
        // Use agent system
        const agentRequest = {
          query: currentInput,
          session_id: currentSessionId,
          language: 'nepali',
          max_results: 5,
          include_sources: true
        };

        const agentResult = await apiService.sendToAgent(agentRequest);
        setAgentResponse(agentResult);
        setSourceNodes(agentResult.sources || []);

        // Update session ID if returned
        if (agentResult.session_id) {
          setCurrentSessionId(agentResult.session_id);
        }

        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: agentResult.content,
          role: 'assistant',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, assistantMessage]);
      } else {
        // Use query endpoint - get AI response with source nodes
        const queryResult = await apiService.queryWithEngine(currentInput);
        setQueryResponse(queryResult.response);
        setSourceNodes(queryResult.source_nodes);

        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: queryResult.response,
          role: 'assistant',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, assistantMessage]);
      }

      // Also load documents for the right panel
      setSearchQuery(currentInput);
      loadDocuments(1, false);
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        content: 'माफ गर्नुहोस्, केही समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।',
        role: 'assistant',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Component event handlers
  const handleInputChange = (value: string) => {
    setInputMessage(value);
  };

  const handleModeChange = (mode: PlaygroundMode) => {
    setPlaygroundMode(mode);
  };

  const handleSourceNodesUpdate = (nodes: RetrieveResponse[]) => {
    setSourceNodes(nodes);
  };

  const handleQueryResponseUpdate = (response: string) => {
    setQueryResponse(response);
  };

  const handleAgentResponseUpdate = (response: AgentResponse) => {
    setAgentResponse(response);
  };

  return (
    <ProtectedLayout>
      <div className="h-full flex gap-6 p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 overflow-hidden">
        {/* Chat Playground Component - 50% width, 90% height */}
        <div className="w-1/2 h-[90vh] flex flex-col bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden backdrop-blur-sm">
          <ChatPlayground
            messages={messages}
            inputMessage={inputMessage}
            isLoading={isLoading}
            playgroundMode={playgroundMode}
            onSendMessage={handleSendMessage}
            onInputChange={handleInputChange}
            onModeChange={handleModeChange}
            onSourceNodesUpdate={handleSourceNodesUpdate}
            onQueryResponseUpdate={handleQueryResponseUpdate}
            onAgentResponseUpdate={handleAgentResponseUpdate}
          />
        </div>

        {/* Documents Browser Component - 50% width, 90% height */}
        <div className="w-1/2 h-[90vh] flex flex-col bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden backdrop-blur-sm">
          <DocumentsBrowser
            searchResults={searchResults}
            sourceNodes={sourceNodes}
            searchQuery={searchQuery}
            isSearching={isSearching}
            hasMore={hasMore}
            onLoadMore={loadMoreDocuments}
          />
        </div>
      </div>
    </ProtectedLayout>
  );
};

export default LegalAssistantPage;
