# app/v1/services/knowledge_base/routes.py

from fastapi import APIRouter, Depends, Query
from typing import Optional

from app.shared.database.models import UserTenantDB
from app.shared.security.dependencies import get_current_user
from app.shared.utils.logger import setup_new_logging
from .service import KnowledgeBaseService

router = APIRouter(prefix="/knowledge-base", tags=["Knowledge Base"])
logger = setup_new_logging(__name__)

@router.get("/retrieve")
async def retrieve_source_nodes(
    query: str,
    max_results: int = Query(5, ge=1, le=50, description="Maximum number of results"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Retrieve source nodes using vector similarity with retriever only.
    Returns raw source nodes without query engine processing.
    """
    service = KnowledgeBaseService(current_user)
    return await service.retrieve_source_nodes(query, max_results)

@router.get("/query")
async def query_with_engine(
    query: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Query using the query engine for full processing.
    Returns processed response with context and source nodes.
    """
    service = KnowledgeBaseService(current_user)
    return await service.query_with_engine(query)

@router.get("/search_all")
async def search_all_documents(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Results per page"),
    offset: Optional[str] = None,
    include_vectors: bool = False,
    filter_by_document: Optional[str] = None,
    filter_by_type: Optional[str] = None,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Scroll through all documents with infinite scroll functionality using Qdrant scroll API only.
    Pure document listing functionality without search/query capabilities.
    No tenant filtering needed since each tenant has their own collection.
    """
    service = KnowledgeBaseService(current_user)
    return await service.search_all_documents(
        page=page,
        page_size=page_size,
        offset=offset,
        include_vectors=include_vectors,
        filter_by_document=filter_by_document,
        filter_by_type=filter_by_type
    )

@router.get("/search_query")
async def search_query(
    query: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Search for relevant document chunks without AI response generation.
    Alternative search endpoint that returns structured results.
    """
    service = KnowledgeBaseService(current_user)
    return await service.search_query(query)
