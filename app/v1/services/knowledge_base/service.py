# app/v1/services/knowledge_base/service.py

from typing import List, Optional
from fastapi import HTTPException
import time
from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from llama_index.core import Settings
from qdrant_client.http import models
import json

logger = setup_new_logging(__name__)

class KnowledgeBaseService:
    """
    Lean knowledge base service with only essential search and scroll functionality.
    Initializes index and retriever once for efficient operations.
    """

    def __init__(self, current_user: UserTenantDB):
        self.current_user = current_user
        self.index = None
        self.retriever = None
        self.query_engine = None
        self.search_collection = None
        self._initialized = False

    async def _initialize(self):
        """Initialize service components with OpenAI embeddings."""
        if self._initialized:
            return

        try:
            # Initialize Qdrant
            self.qdrant_client = await self.current_user.init_qdrant()

            # Get collection settings - use legal_sentence_context as primary search collection
            collection_settings = await self._get_collection_settings()
            self.search_collection = collection_settings["legal_sentence_context"]
            self.split_docs_collection = collection_settings["legal_sentence"]

            # Ensure collection exists (text-embedding-large has 3072 dimensions)
            await self.qdrant_client.create_collection(self.search_collection, vector_size=1536)

            # Configure OpenAI embeddings
            from llama_index.embeddings.openai import OpenAIEmbedding
            from llama_index.llms.openai import OpenAI

            # Set up OpenAI text-embedding-large model
            Settings.embed_model = OpenAIEmbedding(
                model="text-embedding-3-large",
                dimensions=1536,  # text-embedding-3-large supports up to 3072 dimensions
                api_key=self.current_user.env.OPENAI_API_KEY
            )

            # Import LlamaIndex components
            from llama_index.core import VectorStoreIndex
            from llama_index.vector_stores.qdrant import QdrantVectorStore
            from llama_index.core.retrievers import VectorIndexRetriever
            from llama_index.core.query_engine import RetrieverQueryEngine

            # Create vector store with both sync and async clients
            from qdrant_client import QdrantClient as SyncQdrantClient

            # Get Qdrant config for sync client
            qdrant_config = await self.current_user.adb.settings.find_one({"name": "qdrant_config"})
            if not qdrant_config:
                raise HTTPException(status_code=500, detail="Qdrant configuration not found")

            # Create sync client for LlamaIndex
            sync_client = SyncQdrantClient(
                host=qdrant_config["host"],
                port=qdrant_config["port"],
                api_key=qdrant_config.get("api_key"),
                timeout=qdrant_config.get("timeout", 60),
                https=qdrant_config.get("https", False),
                prefer_grpc=False
            )

            # Create vector store with both sync and async clients
            vector_store = QdrantVectorStore(
                client=sync_client,  # Sync client for sync operations
                aclient=self.qdrant_client.client,  # Async client for async operations
                collection_name=self.search_collection,
                enable_hybrid=False
            )

            # Create index from existing vector store
            self.index = VectorStoreIndex.from_vector_store(vector_store)

            # Create retriever (no LLM needed for retrieval)
            self.retriever = VectorIndexRetriever(
                index=self.index,
                similarity_top_k=10,  # Default top k
            )

            # Create query engine with LLM for response generation
            Settings.llm = OpenAI(
                model="gpt-4o-mini",  # Use efficient model for query processing
                temperature=0.1,
                api_key=self.current_user.env.OPENAI_API_KEY
            )

            self.query_engine = RetrieverQueryEngine(
                retriever=self.retriever,
            )
            self._initialized = True
            logger.info(f"Initialized KnowledgeBaseService with OpenAI embeddings for collection: {self.search_collection}")

        except Exception as e:
            logger.error(f"Error initializing KnowledgeBaseService: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to initialize service: {str(e)}")

    async def _get_collection_settings(self) -> dict:
        """
        Get collection names from Qdrant config in MongoDB settings.
        Uses legal_sentence_context collection as primary search collection.
        """
        try:
            # Get Qdrant config directly from tenant settings
            qdrant_config = await self.current_user.adb.settings.find_one({"name": "qdrant_config"})

            if not qdrant_config:
                # Use default collection names with legal_sentence_context as primary
                legal_sentence_collection = "legal_split_documents"
                page_collection = "legal_page"
                legal_sentence_context = "legal_sentence_context"
            else:
                # Get collection names from config
                legal_sentence_collection = qdrant_config.get("legal_sentence", "legal_split_documents")
                page_collection = qdrant_config.get("page_collection", "legal_page")
                legal_sentence_context = qdrant_config.get("legal_sentence_context", "legal_sentence_context")

            return {
                "search_collection": legal_sentence_context,  # Use legal_sentence_context as primary
                "legal_sentence": legal_sentence_collection,
                "page_collection": page_collection,
                "legal_sentence_context": legal_sentence_context
            }
        except Exception as e:
            logger.warning(f"Could not get collection settings from MongoDB, using defaults: {e}")
            # Fallback to default collection names with legal_sentence_context as primary
            return {
                "search_collection": "legal_sentence_context",  # Use legal_sentence_context as primary
                "legal_sentence": "legal_split_documents",
                "page_collection": "legal_page",
                "legal_sentence_context": "legal_sentence_context"
            }

    async def retrieve_source_nodes(self, query: str, max_results: int = 5, offset: Optional[str] = None) -> List[dict]:
        """
        Retrieve source nodes using Qdrant scroll method only.
        Uses scroll with optional filters for search functionality.
        Returns raw source nodes with complete metadata and pagination support.
        """
        try:
            await self._initialize()

            # Build filter conditions for query-based search
            filter_conditions = None
            if query:
                # For text-based search, we can use scroll with text filters
                # This is a simple text matching approach using scroll
                filter_conditions = models.Filter(
                    should=[
                        models.FieldCondition(
                            key="text",
                            match=models.MatchText(text=query)
                        ),
                        models.FieldCondition(
                            key="_node_content",
                            match=models.MatchText(text=query)
                        )
                    ]
                )

            # Use direct Qdrant scroll method
            scroll_result = await self.qdrant_client.client.scroll(
                collection_name=self.search_collection,
                scroll_filter=filter_conditions,
                limit=max_results,
                offset=offset,
                with_payload=True,
                with_vectors=False
            )

            # Extract points and next offset from scroll result
            points, next_page_offset = scroll_result

            # Format results to match expected structure
            results = []
            for point in points:
                payload = point.payload if hasattr(point, 'payload') else {}

                # Extract text content - handle both direct text and _node_content
                text_content = ""
                if "_node_content" in payload:
                    try:
                        import json
                        node_content = json.loads(payload["_node_content"])
                        text_content = node_content.get("text_resource", {}).get("text", "")
                    except (json.JSONDecodeError, KeyError):
                        text_content = payload.get("text", "")
                else:
                    text_content = payload.get("text", "")

                formatted_result = {
                    "id": str(point.id),
                    "score": 1.0,  # Scroll doesn't provide relevance scores
                    "text": text_content,
                    "document_id": payload.get("parent_doc_id", ""),
                    "filename": payload.get("filename", ""),
                    "page_number": payload.get("page_number", 0),
                    "chunk_index": payload.get("chunk_index", 0),
                    "metadata": {
                        "source_name": payload.get("source", payload.get("filename", "unknown.pdf")),
                        "source": payload.get("source", payload.get("filename", "unknown.pdf")),
                        "page_numbers": [payload.get("page_number", 0)] if payload.get("page_number") is not None else [],
                        "page_number": payload.get("page_number", 0),
                        "sent_id": payload.get("sent_id", str(point.id)),
                        "chunk_index": payload.get("chunk_index", 0),
                        "parent_doc_id": payload.get("parent_doc_id", ""),
                        "filename": payload.get("filename", ""),
                        "offset_info": {
                            "current_offset": offset,
                            "next_offset": str(next_page_offset) if next_page_offset else None,
                            "has_more": next_page_offset is not None
                        },
                        **payload  # Include all original metadata
                    }
                }
                results.append(formatted_result)

            logger.info(f"Retrieved {len(results)} source nodes using Qdrant scroll for query: {query}")
            return results

        except Exception as e:
            logger.error(f"Error retrieving source nodes: {e}")
            raise HTTPException(status_code=500, detail=f"Error retrieving source nodes: {str(e)}")

    async def query_with_engine(self, query: str) -> dict:
        """
        Query using the initialized query engine with OpenAI embeddings + LLM.
        Uses embeddings for retrieval AND LLM (GPT-4o-mini) for response generation.
        Returns processed response with context and source nodes.
        """
        try:
            await self._initialize()

            # Query using the query engine (async)
            response = await self.query_engine.aquery(query)

            # Format response
            result = {
                "query": query,
                "response": str(response),
                "source_nodes": []
            }

            # Add source nodes if available
            if hasattr(response, 'source_nodes') and response.source_nodes:
                for node in response.source_nodes:
                    source_node = {
                        "id": node.node.id_,
                        "score": node.score if hasattr(node, 'score') else 1.0,
                        "text": node.node.text,
                        "metadata": node.node.metadata
                    }
                    result["source_nodes"].append(source_node)

            logger.info(f"Processed query with engine: {query}")
            return result

        except Exception as e:
            logger.error(f"Error querying with engine: {e}")
            raise HTTPException(status_code=500, detail=f"Error querying with engine: {str(e)}")

    async def search_all_documents(
        self,
        limit: int = 10,
        offset: Optional[str] = None,
        include_vectors: bool = False,
        filter_by_document: Optional[str] = None,
        filter_by_type: Optional[str] = None
    ) -> dict:
        """
        Paginated search endpoint that returns all documents from Qdrant collection.
        Supports filtering and pagination with offset using echo_bot style implementation.
        Uses order_by with start_from for proper pagination.
        """
        start_time = time.time()

        try:
            await self._initialize()

            # Check if collection has any documents
            total_count = await self.qdrant_client.count_documents(self.search_collection)
            if total_count == 0:
                return {
                    "next_offset": None,
                    "response": [],
                    "has_more": False,
                    "total_count": 0,
                    "processing_time_ms": (time.time() - start_time) * 1000
                }

            # Build filter conditions for document and type filters
            filter_conditions = None
            must_conditions = []

            # Add document filter if specified
            if filter_by_document:
                must_conditions.append({
                    "key": "filename",
                    "match": {"value": filter_by_document}
                })

            # Add type filter if specified
            if filter_by_type:
                must_conditions.append({
                    "key": "type",
                    "match": {"value": filter_by_type}
                })

            # Create filter if we have conditions
            if must_conditions:
                filter_conditions = {"must": must_conditions}

            # Set up scroll parameters using echo_bot style
            scroll_params = {
                "collection_name": self.search_collection,
                "limit": limit,
                "with_payload": True,
                "with_vectors": include_vectors
            }

            # Handle different query scenarios like echo_bot
            if filter_conditions:
                # When using filter
                scroll_params["scroll_filter"] = filter_conditions

                # If we also have an offset with filter, add range condition
                if offset:
                    # Create combined filter with original conditions plus offset range
                    combined_filter = {
                        "must": filter_conditions.get("must", []) + [
                            {
                                "key": "created_at",  # Use created_at for consistent ordering
                                "range": {"lt": offset}
                            }
                        ]
                    }
                    scroll_params["scroll_filter"] = combined_filter
                else:
                    # Add ordering for first page with filter
                    order_by = {
                        "key": "created_at",
                        "direction": "desc"
                    }
                    scroll_params["order_by"] = order_by
            else:
                # When not using filter, use order_by for pagination
                order_by = {
                    "key": "created_at",
                    "direction": "desc"
                }

                # Add start_from for pagination if offset is provided
                if offset:
                    order_by["start_from"] = offset

                scroll_params["order_by"] = order_by

            # Execute the scroll query using direct Qdrant client
            response, _ = await self.qdrant_client.client.scroll(**scroll_params)

            # Handle empty response
            if not response:
                return {
                    "next_offset": None,
                    "response": [],
                    "has_more": False,
                    "total_count": total_count,
                    "processing_time_ms": (time.time() - start_time) * 1000
                }

            # Process results and filter out duplicates like echo_bot
            seen_ids = set()
            unique_response = []

            for item in response:
                item_id = item.id
                if item_id not in seen_ids:
                    seen_ids.add(item_id)
                    unique_response.append(item)

            # Process each result
            processed_results = []
            for point in unique_response:
                payload = point.payload if hasattr(point, 'payload') else {}

                # Extract text content - handle both direct text and _node_content
                text_content = ""
                if "_node_content" in payload:
                    try:
                        import json
                        node_content = json.loads(payload["_node_content"])
                        text_content = node_content.get("text_resource", {}).get("text", "")
                    except (json.JSONDecodeError, KeyError):
                        text_content = payload.get("text", "")
                else:
                    text_content = payload.get("text", "")

                result = {
                    "id": str(point.id),
                    "score": 1.0,  # No relevance score for scroll
                    "text": text_content,
                    "document_id": payload.get("parent_doc_id", ""),
                    "filename": payload.get("filename", ""),
                    "page_number": payload.get("page_number", 0),
                    "chunk_index": payload.get("chunk_index", 0),
                    "metadata": {
                        "source_name": payload.get("source", payload.get("filename", "unknown.pdf")),
                        "source": payload.get("source", payload.get("filename", "unknown.pdf")),
                        "page_numbers": [payload.get("page_number", 0)] if payload.get("page_number") is not None else [],
                        "page_number": payload.get("page_number", 0),
                        "sent_id": payload.get("sent_id", str(point.id)),
                        "chunk_index": payload.get("chunk_index", 0),
                        "parent_doc_id": payload.get("parent_doc_id", ""),
                        "filename": payload.get("filename", ""),
                        "created_at": payload.get("created_at"),
                        "updated_at": payload.get("updated_at"),
                        **payload  # Include all original metadata
                    }
                }
                if include_vectors and hasattr(point, 'vector'):
                    result["vector"] = point.vector
                else:
                    result["vector"] = None
                processed_results.append(result)

            # Determine if there are more results and the next offset like echo_bot
            has_more = len(response) == limit

            # Get the last point's created_at value for next pagination
            last_point = None
            if unique_response:
                # Use the last unique item for pagination
                last_point = unique_response[-1].payload.get("created_at")

                # If we filtered out duplicates, check if there are more results
                if len(unique_response) < limit and has_more:
                    # Do an additional query to check if there are more results
                    additional_params = dict(scroll_params)

                    if filter_conditions:
                        # Create a filter that excludes the results we've already seen
                        combined_filter = {
                            "must": filter_conditions.get("must", []) + [
                                {
                                    "key": "created_at",
                                    "range": {"lt": last_point}
                                }
                            ]
                        }
                        additional_params["scroll_filter"] = combined_filter
                    else:
                        # When using order_by
                        additional_params["order_by"] = {
                            "key": "created_at",
                            "direction": "desc",
                            "start_from": last_point
                        }

                    # Check if there are more results with a limit of 1
                    additional_params["limit"] = 1
                    more_results, _ = await self.qdrant_client.client.scroll(**additional_params)
                    has_more = len(more_results) > 0

            processing_time = (time.time() - start_time) * 1000

            return {
                "next_offset": last_point if has_more else None,
                "response": processed_results,
                "has_more": has_more,
                "total_count": total_count,
                "processing_time_ms": processing_time
            }

        except Exception as e:
            logger.error(f"Error in search_all_documents: {e}")
            raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

    async def search_query(self, query: str) -> dict:
        """
        Search for relevant document chunks without AI response generation using retriever.
        Uses legal_sentence collection for search operations.
        """
        try:
            # Use the retrieve_source_nodes method
            results = await self.retrieve_source_nodes(query=query, max_results=10)

            return {
                "query": query,
                "results": results,
                "total_results": len(results),
                "collection": self.search_collection
            }

        except Exception as e:
            logger.error(f"Error in search_query: {e}")
            raise HTTPException(status_code=500, detail=f"Search query failed: {str(e)}")

    @staticmethod
    async def search_documents(query: str, current_user: UserTenantDB, max_results: int = 5) -> List[dict]:
        """
        Search documents using legal_sentence_context collection and return grouped by PDF source.
        Returns results grouped by PDF source with sentences list page-wise.

        Args:
            query: Search query string
            current_user: Current user with tenant database access
            max_results: Maximum number of results to return

        Returns:
            List of dictionaries where each dict represents a PDF source with grouped sentences
            Format: [{"source": "pdf_name", "sentences": [{"sent_id": "...", "text": "...", "page_number": 1}]}]
        """
        try:
            # Initialize knowledge base service
            kb_service = KnowledgeBaseService(current_user)
            await kb_service._initialize()

            # Use retrieve_source_nodes to get results from legal_sentence_context collection
            source_nodes = await kb_service.retrieve_source_nodes(query, max_results)
            logger.debug(f"Retrieved {len(source_nodes)} source nodes from vector search")

            # Format source nodes into grouped sources
            grouped_sources = await kb_service.format_source_nodes_to_grouped_sources(source_nodes, current_user)
            logger.debug(f"Formatted into {len(grouped_sources)} grouped sources")

            logger.info(f"Found {len(grouped_sources)} grouped sources for query: {query[:50]}...")
            return grouped_sources

        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return []

    async def retrieve_sentences_by_ids(self, sent_ids: List[str], current_user: UserTenantDB) -> List[dict]:
        """
        Retrieve individual sentences by their sent_id from the legal_sentence collection.
        Groups results by PDF source and returns structured response compatible with uniform response format.

        Args:
            sent_ids: List of sentence IDs to retrieve
            current_user: Current user with tenant database access

        Returns:
            List of dictionaries where each dict represents a PDF source with its sentences and page numbers
        """
        try:
            await self._initialize()

            # Get collection settings to access legal_sentence collection
            collection_settings = await self._get_collection_settings()
            legal_sentence_collection = collection_settings["legal_sentence"]

            logger.info(f"Retrieving {len(sent_ids)} sentences from collection: {legal_sentence_collection}")

            # Initialize Qdrant client
            qdrant_client = await current_user.init_qdrant()

            # Dictionary to group results by PDF source
            source_groups = {}

            # Retrieve each sentence by sent_id
            for sent_id in sent_ids:
                try:
                    # Query Qdrant for the specific sent_id
                    scroll_result = await qdrant_client.scroll(
                        collection_name=legal_sentence_collection,
                        scroll_filter=models.Filter(
                            must=[
                                models.FieldCondition(key="sent_id", match=models.MatchValue(value=sent_id))
                            ]
                        ),
                        limit=1,
                    )

                    # Extract points from scroll result
                    points, _ = scroll_result

                    if points:
                        item = points[0]

                        # Extract text from the node content
                        payload = item.dict()["payload"]
                        node_content = payload.get("_node_content")

                        if node_content:
                            # Parse the JSON node content
                            node = json.loads(node_content)
                            text = node["text_resource"]["text"]

                            # Extract metadata
                            metadata = payload.get("metadata", {})
                            source = metadata.get("source", "unknown.pdf")
                            page_number = metadata.get("page_number", 0)

                            # Group by source
                            if source not in source_groups:
                                source_groups[source] = {
                                    "source": source,
                                    "sentences": []
                                }

                            # Add sentence to the source group
                            sentence_data = {
                                "sent_id": sent_id,
                                "text": text,
                                "page_number": page_number
                            }
                            source_groups[source]["sentences"].append(sentence_data)

                            logger.debug(f"Retrieved sentence {sent_id} from {source}, page {page_number}")
                        else:
                            logger.warning(f"No node content found for sent_id: {sent_id}")
                    else:
                        logger.warning(f"No results found for sent_id: {sent_id}")

                except Exception as e:
                    logger.error(f"Error retrieving sentence {sent_id}: {e}")
                    continue

            # Convert grouped results to list format
            result = list(source_groups.values())

            logger.info(f"Successfully retrieved sentences from {len(result)} PDF sources")
            return result

        except Exception as e:
            logger.error(f"Error in retrieve_sentences_by_ids: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to retrieve sentences: {str(e)}")



    async def format_source_nodes_to_grouped_sources(
        self, source_nodes: List[dict], current_user: UserTenantDB
    ) -> List[dict]:
        """
        Format source nodes into grouped sources by PDF name with sentences list page-wise.

        Args:
            source_nodes: List of source nodes from search results
            current_user: Current user with tenant database access

        Returns:
            List of dictionaries where each dict represents a PDF source with grouped sentences
            Format: [{"source": "pdf_name", "sentences": [{"sent_id": "...", "text": "...", "page_number": 1}]}]
        """
        try:
            # Extract sent_ids for batch retrieval
            sent_ids = []
            node_metadata = {}

            for node in source_nodes:
                metadata = node.get('metadata', {})
                sent_id = metadata.get('sent_id', node.get('id', ''))
                if sent_id:
                    sent_ids.append(sent_id)
                    # Store metadata for each sent_id
                    node_metadata[sent_id] = {
                        'source': metadata.get('source', 'unknown.pdf'),
                        'page_number': metadata.get('page_number', 0),
                        'score': node.get('score', 0.0),
                        'fallback_text': node.get('text', '')
                    }

            # Retrieve full sentence content using the new method
            if sent_ids:
                grouped_sources = await self.retrieve_sentences_by_ids(sent_ids, current_user)

                # Enhance with score information from search results
                for source_group in grouped_sources:
                    for sentence in source_group["sentences"]:
                        sent_id = sentence["sent_id"]
                        if sent_id in node_metadata:
                            sentence["score"] = node_metadata[sent_id]["score"]

                logger.info(f"Formatted {len(grouped_sources)} source groups from {len(sent_ids)} sentences")
                return grouped_sources
            else:
                # Fallback: group by source using available metadata
                source_groups = {}
                for node in source_nodes:
                    metadata = node.get('metadata', {})
                    source = metadata.get('source', 'unknown.pdf')

                    if source not in source_groups:
                        source_groups[source] = {
                            "source": source,
                            "sentences": []
                        }

                    sentence_data = {
                        "sent_id": metadata.get('sent_id', node.get('id', '')),
                        "text": node.get('text', ''),
                        "page_number": metadata.get('page_number', 0),
                        "score": node.get('score', 0.0)
                    }
                    source_groups[source]["sentences"].append(sentence_data)

                result = list(source_groups.values())
                logger.info(f"Formatted {len(result)} source groups using fallback method")
                return result

        except Exception as e:
            logger.error(f"Error formatting source nodes to grouped sources: {e}")
            # Return empty list on error
            return []