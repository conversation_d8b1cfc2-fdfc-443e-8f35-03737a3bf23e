
   FastAPI   Starting production server 🚀
 
             Searching for package file structure from directories with         
             __init__.py files                                                  
             Importing from /home/<USER>/Documents/Nextai/legal
 
    module   📁 app            
             └── 🐍 __init__.py
 
      code   Importing the FastAPI app object from the module with the following
             code:                                                              
 
             from app import app
 
       app   Using import string: app:app
 
    server   Server started at http://0.0.0.0:8000
    server   Documentation at http://0.0.0.0:8000/docs
 
             Logs:
 
      INFO   Will watch for changes in these directories:                       
             ['/home/<USER>/Documents/Nextai/legal']
      INFO   Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
      INFO   Started reloader process [7881] using WatchFiles
      INFO   Started server process [7937]
      INFO   Waiting for application startup.
2025-07-21 12:01:37,585 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 12:01:37,592 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 12:01:37,592 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
      INFO   127.0.0.1:38024 - "OPTIONS                                         
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:38024 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:38024 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:38024 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:38024 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
